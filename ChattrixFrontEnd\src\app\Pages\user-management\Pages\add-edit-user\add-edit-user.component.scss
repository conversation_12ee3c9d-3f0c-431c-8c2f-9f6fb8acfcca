/* Add/Edit User Container */
.add-edit-user-container {
  padding: var(--spacing-lg);
  background: var(--bg-main-content); /* Light grayish-white background */
  min-height: 100%; /* Take full height of parent */
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative; /* Ensure proper positioning context */
}

/* Integrated Header Section */
.integrated-header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md)
    var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
  margin-bottom: var(--spacing-lg);
  background: var(--bg-card); /* Match card background */
}

.header-content {
  width: 100%;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

/* Form Section */
.form-section {
  width: 100%;
  max-width: 800px;
}

.form-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-xl);
}

/* Section Titles */
.section-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-secondary);
}

/* Form Section Groups */
.form-section-group {
  margin-bottom: var(--spacing-xl);

  &:last-child {
    margin-bottom: var(--spacing-lg);
  }
}

/* Form Rows and Fields */
.form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);

  &:last-child {
    margin-bottom: 0;
  }
}

:host ::ng-deep .form-field {
  flex: 1;

  &.full-width {
    width: 100%;
  }

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--border-primary);
      border-width: 1px;
    }
  }

  &.mat-focused .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--accent-green);
      border-width: 2px;
    }
  }

  &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--border-secondary);
    }
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }

  .mat-mdc-input-element,
  .mat-mdc-select-trigger {
    color: var(--text-primary);
  }

  .mat-mdc-select-arrow {
    color: var(--text-muted);
  }

  // Error styling
  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: var(--error);
      }
    }
  }

  .mat-mdc-form-field-error {
    color: var(--error);
    font-size: 0.75rem;
  }
}

/* Password Information Section */
.password-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-sm);
}

.info-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 0.9rem;
}

.info-icon {
  color: var(--primary);
  font-size: 1.2rem;
}

.password-list {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);

  li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);

    strong {
      color: var(--primary);
    }
  }
}

.note-text {
  color: var(--text-muted);
  font-size: 0.85rem;
  margin: var(--spacing-sm) 0 0 0;
  font-style: italic;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-secondary);
  margin-top: var(--spacing-lg);
}

.cancel-btn {
  height: 44px; /* Consistent height */
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */
  font-weight: 500;

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    color: #ffffff !important;
    border-color: #ffffff !important;
  }
}

.submit-btn {
  min-width: 140px;
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */

  // Override spinner button colors for black/white theme
  .button-content {
    .button-spinner {
      color: #ffffff !important;

      ::ng-deep circle {
        stroke: #ffffff !important;
      }
    }

    .button-text {
      mat-icon {
        color: #ffffff !important;
      }

      span {
        color: #ffffff !important;
      }
    }
  }

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    color: #ffffff !important;
    border-color: #ffffff !important;
  }

  &:disabled {
    opacity: 0.6;
    background: #666666 !important;
    color: #ffffff !important;
    border-color: #ffffff !important;

    .button-content {
      .button-spinner {
        color: #ffffff !important;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .add-edit-user-container {
    padding: var(--spacing-md);
    align-items: stretch; /* Allow full width on mobile */
  }

  .header-section,
  .form-section {
    max-width: none; /* Remove max-width constraint on mobile */
  }

  .form-card {
    padding: var(--spacing-lg);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .form-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .add-edit-user-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .form-card {
    background: #ffffff; /* White card */
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .section-title {
    color: #333333;
    border-bottom-color: #f0f0f0;
  }

  .page-title {
    color: #333333;
  }

  .page-subtitle {
    color: #666666;
  }

  /* Cancel button styling handled by global .cancel-btn class */

  /* Form field styling for light theme */
  :host ::ng-deep .form-field {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #e0e0e0;
      }
    }

    &.mat-focused .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #10b981;
        border-width: 2px;
      }
    }

    &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #d0d0d0;
      }
    }

    .mat-mdc-form-field-label {
      color: #666666;
    }

    .mat-mdc-input-element,
    .mat-mdc-select-trigger {
      color: #333333;
    }

    .mat-mdc-select-arrow {
      color: #999999;
    }

    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336;
        }
      }
    }

    .mat-mdc-form-field-error {
      color: #f44336;
    }
  }
}

/* Dark Theme Overrides */
:host-context(.dark-theme) {
  .add-edit-user-container {
    background: var(--bg-main-content); /* Keep main background consistent */
  }

  .form-card {
    background: #2a2a2a; /* Greyish background for dark mode */
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .section-title {
    color: #ffffff;
    border-bottom-color: #404040;
  }

  .page-title {
    color: #ffffff;
  }

  .page-subtitle {
    color: #b0b0b0;
  }

  /* Cancel button styling handled by global .cancel-btn class */

  /* Form field styling for dark theme */
  :host ::ng-deep .form-field {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #404040;
      }
    }

    &.mat-focused .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #10b981;
        border-width: 2px;
      }
    }

    &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #505050;
      }
    }

    .mat-mdc-form-field-label {
      color: #b0b0b0 !important;
    }

    .mat-mdc-floating-label {
      color: #b0b0b0 !important;
    }

    &.mat-focused .mat-mdc-floating-label {
      color: #10b981 !important;
    }

    .mat-mdc-input-element,
    .mat-mdc-select-trigger {
      color: #ffffff;
    }

    .mat-mdc-select-arrow {
      color: #b0b0b0;
    }

    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336;
        }
      }
    }

    .mat-mdc-form-field-error {
      color: #f44336;
    }
  }
}
