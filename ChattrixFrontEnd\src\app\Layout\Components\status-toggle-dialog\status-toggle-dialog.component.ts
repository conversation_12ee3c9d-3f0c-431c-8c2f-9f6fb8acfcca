import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UserDetails } from '../../../Pages/user-management/Models/UserManagement';

export interface StatusToggleDialogData {
  user: UserDetails;
  currentUserRoles: string[];
}

@Component({
  selector: 'app-status-toggle-dialog',
  standalone: false,
  templateUrl: './status-toggle-dialog.component.html',
  styleUrl: './status-toggle-dialog.component.scss',
})
export class StatusToggleDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<StatusToggleDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: StatusToggleDialogData,
  ) {}

  get actionText(): string {
    return this.data.user.isActive ? 'deactivate' : 'activate';
  }

  get actionIcon(): string {
    return this.data.user.isActive ? 'block' : 'check_circle';
  }

  get actionClass(): string {
    return this.data.user.isActive ? 'deactivate' : 'activate';
  }

  getUserDisplayName(): string {
    return this.data.user.fullName || this.data.user.email || 'Unknown User';
  }

  getUserRoleDisplay(): string {
    if (Array.isArray(this.data.user.roles)) {
      return this.data.user.roles.join(', ');
    }
    return this.data.user.roles as string || 'User';
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onConfirm(): void {
    this.dialogRef.close({
      confirmed: true,
      user: this.data.user,
      newStatus: !this.data.user.isActive,
    });
  }
}
