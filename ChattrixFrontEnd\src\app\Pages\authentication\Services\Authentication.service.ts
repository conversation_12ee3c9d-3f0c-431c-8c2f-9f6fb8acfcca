import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '../../../../Environments/environment';
import { catchError, Observable, tap, finalize } from 'rxjs';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  ApiResponse,
  LoginResponse,
  RegisterResponse,
  OtpVerificationResponse,
  ForgotPasswordResponse,
  ResetPasswordResponse,
  VerifyResetTokenResponse,
  OtpVerificationRequest,
  ResendOtpRequest,
  ForgotPasswordRequest,
  VerifyResetTokenRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  UserInfo,
  TokenPayload,
  AuthError,
} from '../Models';
import { AuthErrorHandlerService } from './AuthErrorHandler.service';
import { SecureStorageService } from './SecureStorage.service';
import { TokenValidatorService } from './TokenValidator.service';
import { InputValidatorService } from './InputValidator.service';
import { AuthStateService } from './AuthState.service';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly errorHandler: AuthErrorHandlerService,
    private readonly storage: SecureStorageService,
    private readonly tokenValidator: TokenValidatorService,
    private readonly inputValidator: InputValidatorService,
    private readonly authState: AuthStateService,
    private readonly router: Router,
  ) {
    // Initialize authentication state on service creation
    this.initializeAuthState();
  }

  /**
   * Authenticates a user with email and password
   */
  login(data: LoginRequest): Observable<LoginResponse> {
    // Validate input
    const validation = this.inputValidator.validateLoginRequest(data);
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors).flat().join('; ');
      this.authState.setLoginFailure(errorMessage);
      throw new Error(errorMessage);
    }

    // Sanitize input
    const sanitizedData: LoginRequest = {
      email: this.inputValidator.sanitizeEmail(data.email),
      password: data.password, // Don't sanitize password as it might contain special chars
    };

    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .post<LoginResponse>(`${this.API_URL}/Login`, sanitizedData)
      .pipe(
        tap((response) => {
          if (response.isSuccess && response.token) {
            const userInfo = this.mapTokenToUserInfo(response.token);
            if (userInfo) {
              this.storage.setToken(response.token);
              this.storage.setUserInfo(userInfo);
              this.authState.setLoginSuccess(userInfo, response.token);
            }
          } else if (response.data?.requiresOtp || response.data?.RequiresOtp) {
            // Handle 2FA requirement
            this.authState.setLoading(false);
          }
        }),
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setLoginFailure(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Registers a new user
   */
  register(data: RegisterRequest): Observable<RegisterResponse> {
    // Validate input
    const validation = this.inputValidator.validateRegisterRequest(data);
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors).flat().join('; ');
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    // Sanitize input
    const sanitizedData: RegisterRequest = {
      fullName: this.inputValidator.sanitizeString(data.fullName),
      email: this.inputValidator.sanitizeEmail(data.email),
      password: data.password, // Don't sanitize password
      phoneNumber: data.phoneNumber
        ? this.inputValidator.sanitizePhoneNumber(data.phoneNumber)
        : undefined,
      description: data.description
        ? this.inputValidator.sanitizeString(data.description)
        : undefined,
    };

    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .post<RegisterResponse>(`${this.API_URL}/Register`, sanitizedData)
      .pipe(
        tap((response) => {
          if (response.isSuccess) {
            // Registration successful - user might need to verify email or login
            this.authState.setLoading(false);
          }
        }),
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Verifies OTP for 2FA login
   */
  loginWith2FA(
    userId: string,
    otp: string,
  ): Observable<OtpVerificationResponse> {
    // Validate input
    const validation = this.inputValidator.validateOtpRequest({ userId, otp });
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors).flat().join('; ');
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    this.authState.setLoading(true);
    this.authState.clearError();

    const requestData: OtpVerificationRequest = { userId, otp };

    return this.httpClient
      .post<OtpVerificationResponse>(`${this.API_URL}/VerifyOtp`, requestData)
      .pipe(
        tap((response) => {
          if (response.isSuccess && response.token) {
            const userInfo = this.mapTokenToUserInfo(response.token);
            if (userInfo) {
              this.storage.setToken(response.token);
              this.storage.setUserInfo(userInfo);
              this.authState.setLoginSuccess(userInfo, response.token);
            }
          }
        }),
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Resends OTP for 2FA
   */
  resendOtp(userId: string): Observable<AuthResponse> {
    if (!userId) {
      const errorMessage = 'User ID is required';
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    this.authState.setLoading(true);
    this.authState.clearError();

    const requestData: ResendOtpRequest = { userId };

    return this.httpClient
      .post<AuthResponse>(`${this.API_URL}/ResendOtp`, requestData)
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Initiates password reset process
   */
  forgotPassword(
    data: ForgotPasswordRequest,
  ): Observable<ForgotPasswordResponse> {
    // Validate input
    const validation = this.inputValidator.validateForgotPasswordRequest(data);
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors).flat().join('; ');
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    // Sanitize input
    const sanitizedData: ForgotPasswordRequest = {
      email: this.inputValidator.sanitizeEmail(data.email),
    };

    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .post<ForgotPasswordResponse>(
        `${this.API_URL}/ForgotPassword`,
        sanitizedData,
      )
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Verifies password reset token
   */
  verifyResetToken(
    data: VerifyResetTokenRequest,
  ): Observable<VerifyResetTokenResponse> {
    if (!data.email || !data.resetToken) {
      const errorMessage = 'Email and reset token are required';
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    // Sanitize input
    const sanitizedData: VerifyResetTokenRequest = {
      email: this.inputValidator.sanitizeEmail(data.email),
      resetToken: data.resetToken.trim(),
    };

    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .post<VerifyResetTokenResponse>(
        `${this.API_URL}/VerifyResetToken`,
        sanitizedData,
      )
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Resets user password with token
   */
  resetPassword(data: ResetPasswordRequest): Observable<ResetPasswordResponse> {
    // Validate input
    const validation = this.inputValidator.validateResetPasswordRequest(data);
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors).flat().join('; ');
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    // Sanitize input
    const sanitizedData: ResetPasswordRequest = {
      email: this.inputValidator.sanitizeEmail(data.email),
      resetToken: data.resetToken.trim(),
      newPassword: data.newPassword,
      confirmPassword: data.confirmPassword,
    };

    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .post<ResetPasswordResponse>(
        `${this.API_URL}/ResetPassword`,
        sanitizedData,
      )
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  // ===== PUBLIC API METHODS =====

  /**
   * Gets the current authentication token
   */
  getToken(): string | null {
    return this.storage.getToken();
  }

  /**
   * Gets the current user information
   */
  getUserInfo(): UserInfo | null {
    return this.storage.getUserInfo();
  }

  /**
   * Checks if user is currently logged in
   */
  isLoggedIn(): boolean {
    const token = this.getToken();
    return token !== null && !this.tokenValidator.isTokenExpired(token);
  }

  /**
   * Checks if the current token is expired
   */
  isTokenExpired(): boolean {
    const token = this.getToken();
    return this.tokenValidator.isTokenExpired(token);
  }

  /**
   * Checks if user has a specific role
   */
  hasRole(role: string): boolean {
    const userInfo = this.getUserInfo();
    if (!userInfo || !userInfo.role) {
      return false;
    }

    if (Array.isArray(userInfo.role)) {
      return userInfo.role.includes(role);
    } else {
      return userInfo.role === role;
    }
  }

  /**
   * Logs out the current user
   */
  logout(): void {
    this.storage.clearAll();
    this.authState.setLogoutState();
    // Navigate to login page after logout
    this.router.navigate(['/auth/login']);
  }

  /**
   * Refreshes the user session if token is still valid
   */
  refreshSession(): void {
    if (this.isTokenExpired()) {
      this.logout();
    } else {
      const token = this.getToken();
      if (token) {
        const userInfo = this.mapTokenToUserInfo(token);
        if (userInfo) {
          this.storage.setUserInfo(userInfo);
          this.authState.setUser(userInfo);
        }
      }
    }
  }

  /**
   * Refreshes user info from the current token
   */
  refreshUserInfoFromToken(): boolean {
    const token = this.getToken();
    if (token && !this.isTokenExpired()) {
      const userInfo = this.mapTokenToUserInfo(token);
      if (userInfo) {
        this.storage.setUserInfo(userInfo);
        this.authState.setUser(userInfo);
        return true;
      }
    }
    return false;
  }

  // ===== OBSERVABLE GETTERS FOR REACTIVE PROGRAMMING =====

  /**
   * Observable for authentication state
   */
  get authState$() {
    return this.authState.authState$;
  }

  /**
   * Observable for authentication status
   */
  get isAuthenticated$() {
    return this.authState.isAuthenticated$;
  }

  /**
   * Observable for loading status
   */
  get isLoading$() {
    return this.authState.isLoading$;
  }

  /**
   * Observable for current user
   */
  get user$() {
    return this.authState.user$;
  }

  /**
   * Observable for errors
   */
  get error$() {
    return this.authState.error$;
  }

  /**
   * Observable for user roles
   */
  get userRoles$() {
    return this.authState.userRoles$;
  }

  /**
   * Observable for admin status
   */
  get isAdmin$() {
    return this.authState.isAdmin$;
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Initializes authentication state on service creation
   */
  public initializeAuthState(): void {
    const token = this.storage.getToken();
    const userInfo = this.storage.getUserInfo();

    if (token && !this.tokenValidator.isTokenExpired(token) && userInfo) {
      this.authState.setLoginSuccess(userInfo, token);
    } else {
      // Clear invalid/expired data
      this.storage.clearAll();
      this.authState.reset();
    }
  }

  /**
   * Maps JWT token payload to UserInfo interface
   */
  private mapTokenToUserInfo(token: string): UserInfo | null {
    const decodedToken = this.tokenValidator.decodeToken(token);
    if (!decodedToken) {
      return null;
    }

    // Use TokenValidator's getUserRoles method for proper role extraction
    const roles = this.tokenValidator.getUserRoles(token);

    return {
      id: decodedToken.id || decodedToken.nameid || '',
      name: decodedToken.FullName || decodedToken.name || '',
      email: decodedToken.Email || decodedToken.email || '',
      role: roles, // Use the properly extracted roles
      isActive: decodedToken.IsActive || true,
      phoneNumber: decodedToken.PhoneNumber || undefined,
      description: decodedToken.Description || undefined,
      profilePictureUrl: decodedToken.ProfilePictureUrl || undefined,
    };
  }

  /**
   * Debug method to log token information (development only)
   */
  debugToken(): void {
    if (!environment.production) {
      const token = this.getToken();
      if (token) {
        const decoded = this.tokenValidator.decodeToken(token);
        const roles = this.tokenValidator.getUserRoles(token);
        const userInfo = this.mapTokenToUserInfo(token);
        const currentTime = Math.floor(Date.now() / 1000);

        console.log('=== TOKEN DEBUG INFO ===');
        console.log('Token (first 20 chars):', token.substring(0, 20) + '...');
        console.log('Current Unix timestamp:', currentTime);
        console.log('Current Date:', new Date());
        console.log('Decoded token payload:', decoded);

        if (decoded) {
          console.log('Token timestamps:');
          console.log(
            '  - iat (issued at):',
            decoded['iat'],
            '→',
            decoded['iat'] ? new Date(decoded['iat'] * 1000) : 'N/A',
          );
          console.log(
            '  - nbf (not before):',
            decoded['nbf'],
            '→',
            decoded['nbf'] ? new Date(decoded['nbf'] * 1000) : 'N/A',
          );
          console.log(
            '  - exp (expires at):',
            decoded['exp'],
            '→',
            decoded['exp'] ? new Date(decoded['exp'] * 1000) : 'N/A',
          );

          if (decoded['nbf'] && decoded['nbf'] > currentTime) {
            console.log('⚠️  TOKEN NOT YET VALID - nbf is in the future!');
          }
          if (decoded['exp'] && decoded['exp'] < currentTime) {
            console.log('❌ TOKEN EXPIRED');
          }
          if (decoded['iat'] && decoded['iat'] > currentTime) {
            console.log(
              '⚠️  TOKEN ISSUED IN THE FUTURE - possible clock sync issue!',
            );
          }
        }

        console.log('Extracted roles:', roles);
        console.log('Mapped user info:', userInfo);
        console.log(
          'Is expired (by validator):',
          this.tokenValidator.isTokenExpired(token),
        );
        console.log(
          'Remaining time (ms):',
          this.tokenValidator.getTokenRemainingTime(token),
        );
        console.log('========================');
      } else {
        console.log('No token found');
      }
    }
  }

  /**
   * Debug method to test API call with current token
   */
  debugApiCall(): void {
    if (!environment.production) {
      const token = this.getToken();
      console.log('=== API CALL DEBUG ===');
      console.log('Token exists:', !!token);
      console.log('API URL:', `${this.API_URL}/GetUsers`);

      if (token) {
        // Test a simple API call to see the actual request/response
        this.httpClient
          .get(`${this.API_URL}/GetUsers?pageNumber=1&pageSize=1`, {
            headers: { Authorization: `Bearer ${token}` },
          })
          .subscribe({
            next: (response) => {
              console.log('✅ API call successful:', response);
            },
            error: (error) => {
              console.log('❌ API call failed:', error);
              console.log('Error status:', error.status);
              console.log('Error message:', error.message);
              console.log('Error response:', error.error);
            },
          });
      }
      console.log('========================');
    }
  }

  /**
   * Changes user password for authenticated users
   */
  changePassword(data: ChangePasswordRequest): Observable<ApiResponse> {
    // Validate input
    if (!data.currentPassword || !data.newPassword || !data.confirmPassword) {
      const errorMessage = 'All password fields are required';
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    if (data.newPassword !== data.confirmPassword) {
      const errorMessage = 'New password and confirmation do not match';
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    // Basic password strength validation
    if (data.newPassword.length < 8) {
      const errorMessage = 'New password must be at least 8 characters long';
      this.authState.setError(errorMessage);
      throw new Error(errorMessage);
    }

    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .post<ApiResponse>(`${this.API_URL}/ChangePassword`, data)
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          throw authError;
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Public method to trigger token debugging
   */
  public debugCurrentToken(): void {
    this.debugToken();
  }
}
