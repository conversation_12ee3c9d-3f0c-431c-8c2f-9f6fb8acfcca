import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, finalize, throwError } from 'rxjs';
import { environment } from '../../../../Environments/environment';
import { AuthErrorHandlerService } from '../../authentication/Services/AuthErrorHandler.service';
import { AuthStateService } from '../../authentication/Services/AuthState.service';
import { ApiResponse } from '../../authentication/Models';

// Profile update request interface
export interface ProfileUpdateRequest {
  fullName?: string;
  phoneNumber?: string;
  description?: string;
  profileImage?: File;
}

// Profile update response interface
export interface ProfileUpdateResponse extends ApiResponse<any> {}

@Injectable({
  providedIn: 'root',
})
export class ProfileUpdateService {
  private readonly API_URL = `${environment.apiUrl}/Account`;

  constructor(
    private httpClient: HttpClient,
    private errorHandler: AuthErrorHandlerService,
    private authState: AuthStateService,
  ) {}

  /**
   * Updates the current user's profile information
   */
  updateProfile(data: ProfileUpdateRequest): Observable<ProfileUpdateResponse> {
    // Validate input
    if (
      !data.fullName &&
      !data.phoneNumber &&
      !data.description &&
      !data.profileImage
    ) {
      const errorMessage = 'At least one field must be provided for update';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Basic validation for full name
    if (data.fullName && data.fullName.trim().length < 2) {
      const errorMessage = 'Full name must be at least 2 characters long';
      this.authState.setError(errorMessage);
      return throwError(() => new Error(errorMessage));
    }

    // Basic validation for phone number
    if (data.phoneNumber && data.phoneNumber.trim().length > 0) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(data.phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
        const errorMessage = 'Please enter a valid phone number';
        this.authState.setError(errorMessage);
        return throwError(() => new Error(errorMessage));
      }
    }

    this.authState.setLoading(true);
    this.authState.clearError();

    // Create FormData for file upload support
    const formData = new FormData();

    if (data.fullName) {
      formData.append('fullName', data.fullName);
    }

    if (data.phoneNumber) {
      formData.append('phoneNumber', data.phoneNumber);
    }

    if (data.description) {
      formData.append('description', data.description);
    }

    if (data.profileImage) {
      formData.append('profileImage', data.profileImage);
    }

    return this.httpClient
      .put<ProfileUpdateResponse>(`${this.API_URL}/UpdateProfile`, formData)
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return throwError(() => authError);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Gets the current user's profile information
   */
  getCurrentProfile(): Observable<ApiResponse<any>> {
    this.authState.setLoading(true);
    this.authState.clearError();

    return this.httpClient
      .get<ApiResponse<any>>(`${this.API_URL}/GetCurrentUser`)
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return throwError(() => authError);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }
}
