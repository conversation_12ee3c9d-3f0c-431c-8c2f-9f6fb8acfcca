/* Logout Confirmation Dialog */
.logout-confirmation-dialog {
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Dialog Header */
.dialog-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;

  .warning-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: var(--error);
  }
}

/* Dialog Content */
.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
}

.confirmation-message {
  text-align: center;

  p {
    color: var(--text-primary);
    font-size: 1rem;
    margin: 0 0 var(--spacing-sm) 0;
    font-weight: 500;
  }

  .sub-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 400;
  }
}

/* Dialog Actions */
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.cancel-button {
  background: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 500;
  transition: all var(--transition-fast);

  &:hover {
    background: var(--bg-hover);
    border-color: var(--text-secondary);
  }
}

.logout-button {
  background: var(--error);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 500;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);

  &:hover {
    background: #d32f2f;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .logout-confirmation-dialog {
    max-width: 95vw;
    max-height: 95vh;
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding: var(--spacing-md);
  }

  .dialog-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .cancel-button,
  .logout-button {
    width: 100%;
    justify-content: center;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .dialog-header,
  .dialog-actions {
    background: #f5f5f5;
    border-color: #e0e0e0;
  }

  .dialog-content {
    background: #ffffff;
  }

  .dialog-title {
    color: #333333;
  }

  .confirmation-message p {
    color: #333333;
  }

  .confirmation-message .sub-message {
    color: #666666;
  }

  .cancel-button {
    border-color: #e0e0e0;
    color: #333333;

    &:hover {
      background: #f0f0f0;
      border-color: #999999;
    }
  }
}
