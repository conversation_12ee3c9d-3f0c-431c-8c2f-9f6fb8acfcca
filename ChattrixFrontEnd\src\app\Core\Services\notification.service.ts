import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarRef, SimpleSnackBar } from '@angular/material/snack-bar';

export enum NotificationType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

export interface NotificationConfig {
  duration?: number;
  action?: string;
  horizontalPosition?: 'start' | 'center' | 'end' | 'left' | 'right';
  verticalPosition?: 'top' | 'bottom';
  panelClass?: string | string[];
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly defaultConfig: MatSnackBarConfig = {
    duration: 5000,
    horizontalPosition: 'right',
    verticalPosition: 'top',
    panelClass: []
  };

  private activeSnackbars: MatSnackBarRef<SimpleSnackBar>[] = [];

  constructor(private snackBar: MatSnackBar) {}

  /**
   * Shows a success notification
   */
  showSuccess(message: string, config?: NotificationConfig): MatSnackBarRef<SimpleSnackBar> {
    return this.show(message, NotificationType.SUCCESS, config);
  }

  /**
   * Shows an error notification
   */
  showError(message: string, config?: NotificationConfig): MatSnackBarRef<SimpleSnackBar> {
    return this.show(message, NotificationType.ERROR, config);
  }

  /**
   * Shows a warning notification
   */
  showWarning(message: string, config?: NotificationConfig): MatSnackBarRef<SimpleSnackBar> {
    return this.show(message, NotificationType.WARNING, config);
  }

  /**
   * Shows an info notification
   */
  showInfo(message: string, config?: NotificationConfig): MatSnackBarRef<SimpleSnackBar> {
    return this.show(message, NotificationType.INFO, config);
  }

  /**
   * Shows a notification with the specified type
   */
  private show(
    message: string, 
    type: NotificationType, 
    config?: NotificationConfig
  ): MatSnackBarRef<SimpleSnackBar> {
    const snackBarConfig = this.buildConfig(type, config);
    
    const snackBarRef = this.snackBar.open(
      message, 
      config?.action || 'Close', 
      snackBarConfig
    );

    // Track active snackbars for stacking
    this.activeSnackbars.push(snackBarRef);

    // Remove from tracking when dismissed
    snackBarRef.afterDismissed().subscribe(() => {
      const index = this.activeSnackbars.indexOf(snackBarRef);
      if (index > -1) {
        this.activeSnackbars.splice(index, 1);
      }
    });

    return snackBarRef;
  }

  /**
   * Builds the snackbar configuration
   */
  private buildConfig(type: NotificationType, config?: NotificationConfig): MatSnackBarConfig {
    const panelClasses = [`${type}-snackbar`, 'custom-snackbar'];
    
    if (config?.panelClass) {
      if (Array.isArray(config.panelClass)) {
        panelClasses.push(...config.panelClass);
      } else {
        panelClasses.push(config.panelClass);
      }
    }

    return {
      ...this.defaultConfig,
      ...config,
      panelClass: panelClasses
    };
  }

  /**
   * Dismisses all active notifications
   */
  dismissAll(): void {
    this.snackBar.dismiss();
    this.activeSnackbars = [];
  }

  /**
   * Gets the count of active notifications
   */
  getActiveCount(): number {
    return this.activeSnackbars.length;
  }
}
