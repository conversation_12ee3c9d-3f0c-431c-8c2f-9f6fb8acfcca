// OTP verification request
export interface OtpVerificationRequest {
  userId: string;
  otp: string;
}

// Resend OTP request
export interface ResendOtpRequest {
  userId: string;
}

// Forgot password request
export interface ForgotPasswordRequest {
  email: string;
}

// Verify reset token request
export interface VerifyResetTokenRequest {
  email: string;
  resetToken: string;
}

// Reset password request
export interface ResetPasswordRequest {
  email: string;
  resetToken: string;
  newPassword: string;
  confirmPassword: string;
}

// Change password request
export interface ChangePasswordRequest {
  userId: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword?: string; // Optional for frontend validation only
}
