import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { AuthStateService } from './Pages/authentication/Services/AuthState.service';
import { AuthenticationService } from './Pages/authentication/Services/Authentication.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  title = 'ChattrixFrontEnd';
  isLoading = true;
  isAuthenticated = false;

  constructor(
    private authState: AuthStateService,
    private authService: AuthenticationService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication state first
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        // Use setTimeout to defer the state changes to the next tick
        // This prevents ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
          this.isLoading = state.isLoading;
          this.isAuthenticated = state.isAuthenticated;
          this.cdr.detectChanges();
        }, 0);
      });

    // Initialize authentication service after subscription is set up
    // Use setTimeout to defer initialization to next tick
    setTimeout(() => {
      this.authService.initializeAuthState();
    }, 0);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
