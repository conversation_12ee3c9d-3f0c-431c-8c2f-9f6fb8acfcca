import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { UserManagementService } from '../../Services/UserManagement.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import {
  UserDetails,
  LoadingStates,
  ErrorStates,
} from '../../Models/UserManagement';

@Component({
  selector: 'app-user-details',
  standalone: false,
  templateUrl: './user-details.component.html',
  styleUrl: './user-details.component.scss',
})
export class UserDetailsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  user: UserDetails | null = null;
  userId: string | null = null;
  isLoading = false;

  // State management
  loadingStates: LoadingStates = {
    fetchingUsers: false,
    deletingUser: false,
    updatingUser: false,
    addingUser: false,
  };

  errorStates: ErrorStates = {
    fetchError: null,
    deleteError: null,
    updateError: null,
    addError: null,
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
    private notificationService: NotificationService,
  ) {}

  ngOnInit(): void {
    this.setupSubscriptions();
    this.loadUserFromRoute();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSubscriptions(): void {
    // Subscribe to loading states
    this.userManagementService.loadingStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.loadingStates = states;
      });

    // Subscribe to error states
    this.userManagementService.errorStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.errorStates = states;
        this.handleErrors(states);
      });
  }

  private loadUserFromRoute(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.loadUserDetails(this.userId);
    } else {
      this.showError('User ID not found');
      this.navigateToUserList();
    }
  }

  private loadUserDetails(userId: string): void {
    this.isLoading = true;
    this.userManagementService
      .getUserById(userId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (user: UserDetails) => {
          this.user = user;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading user details:', error);
          this.showError(`Failed to load user details: ${error.message}`);
          this.isLoading = false;
          this.user = null;
        },
      });
  }

  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ');
      if (names.length >= 2) {
        return names[0].charAt(0) + names[1].charAt(0);
      }
      return names[0].charAt(0);
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  formatDate(date: Date): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getRoleClass(role: string): string {
    const normalizedRole = role.toLowerCase().replace(' ', '-');
    return `role-${normalizedRole}`;
  }

  getRoleDisplayName(role: string): string {
    return role.charAt(0).toUpperCase() + role.slice(1);
  }

  getRoleIcon(role: string): string {
    const roleIcons = {
      User: 'person',
      Admin: 'admin_panel_settings',
      'Super Admin': 'supervisor_account',
    };
    return roleIcons[role as keyof typeof roleIcons] || 'person';
  }

  onEditUser(): void {
    if (this.user?.id) {
      this.router.navigate(['/user-management/edit', this.user.id]);
    }
  }

  onBackToList(): void {
    this.navigateToUserList();
  }

  private navigateToUserList(): void {
    this.router.navigate(['/user-management/list']);
  }

  private handleErrors(errorStates: ErrorStates): void {
    if (errorStates.fetchError) {
      this.showError(`Failed to load user: ${errorStates.fetchError}`);
    }
  }

  private showError(message: string): void {
    this.notificationService.showError(message);
  }

  private showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }
}
