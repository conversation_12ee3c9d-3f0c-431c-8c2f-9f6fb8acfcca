[{"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "Register", "RelativePath": "api/Account/AddAdmin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ProfileImageUrl", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "AddUser", "RelativePath": "api/Account/AddUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ProfileImageUrl", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "ChangePassword", "RelativePath": "api/Account/ChangePassword", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.ChangePasswordModel.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "DeleteSqlUser", "RelativePath": "api/Account/DeleteSqlUser", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "DeleteUser", "RelativePath": "api/Account/DeleteUser/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.ToggleStatusRequestModel.ToggleStatusRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "ForgotPassword", "RelativePath": "api/Account/ForgotPassword", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.ForgotPasswordModel.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "GetAllUsers", "RelativePath": "api/Account/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.ApiResponse`1[[System.Collections.Generic.List`1[[ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel.UserDetails, ChattrixBackend.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "GetUserById", "RelativePath": "api/Account/GetUserById/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "GetPagedUsers", "RelativePath": "api/Account/GetUsers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortField", "Type": "System.String", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Role", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.ApiResponse`1[[ChattrixBackend.Core.Pagination.PagedResponseModel.PagedResponse`1[[ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel.UserDetails, ChattrixBackend.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], ChattrixBackend.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Account/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "login", "Type": "ChattrixBackend.Core.Entities.UserManagement.LoginModel.Login", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "AddAdmin", "RelativePath": "api/Account/Register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ProfileImageUrl", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "ResendOtp", "RelativePath": "api/Account/ResendOtp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.ResendOtpRequestModel.ResendOtpRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "ResetPassword", "RelativePath": "api/Account/ResetPassword", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.ResetPasswordModel.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "UpdateProfile", "RelativePath": "api/Account/UpdateProfile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ProfileImageUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "UpdateUser", "RelativePath": "api/Account/UpdateUser/{userId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "userDetails", "Type": "ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel.UserDetails", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "VerifyOtp", "RelativePath": "api/Account/VerifyOtp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.VerifyOtpRequestModel.VerifyOtpRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.AccountController", "Method": "VerifyResetToken", "RelativePath": "api/Account/VerifyResetToken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Core.Entities.UserManagement.VerifyResetTokenModel.VerifyResetTokenRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "GetConversations", "RelativePath": "api/Chat/conversations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "GetConversation", "RelativePath": "api/Chat/conversations/{conversationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "UpdateConversation", "RelativePath": "api/Chat/conversations/{conversationId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Controllers.UpdateConversationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "UploadFile", "RelativePath": "api/Chat/conversations/{conversationId}/files", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "LeaveConversation", "RelativePath": "api/Chat/conversations/{conversationId}/leave", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "SendMessage", "RelativePath": "api/Chat/conversations/{conversationId}/messages", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Controllers.SendMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "GetMessages", "RelativePath": "api/Chat/conversations/{conversationId}/messages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "MarkMessagesAsRead", "RelativePath": "api/Chat/conversations/{conversationId}/messages/read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Controllers.MarkMessagesAsReadRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "AddParticipant", "RelativePath": "api/Chat/conversations/{conversationId}/participants", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Controllers.AddParticipantRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "GetConversationParticipants", "RelativePath": "api/Chat/conversations/{conversationId}/participants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "RemoveParticipant", "RelativePath": "api/Chat/conversations/{conversationId}/participants/{participantId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.String", "IsRequired": true}, {"Name": "participantId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "CreateGroupConversation", "RelativePath": "api/Chat/conversations/group", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Controllers.CreateGroupConversationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "CreatePrivateConversation", "RelativePath": "api/Chat/conversations/private", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Controllers.CreatePrivateConversationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "EditMessage", "RelativePath": "api/Chat/messages/{messageId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Controllers.EditMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "DeleteMessage", "RelativePath": "api/Chat/messages/{messageId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "UpdateMessageStatus", "RelativePath": "api/Chat/messages/{messageId}/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "ChattrixBackend.Controllers.UpdateMessageStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "GetUnreadMessagesCount", "RelativePath": "api/Chat/messages/unread/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "SearchConversations", "RelativePath": "api/Chat/search/conversations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.ChatController", "Method": "SearchMessages", "RelativePath": "api/Chat/search/messages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}, {"Name": "conversationId", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Server.API.Controllers.FilesController", "Method": "GetFile", "RelativePath": "api/Files/{folderName}/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "folderName", "Type": "System.String", "IsRequired": true}, {"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.PresenceController", "Method": "GetOnlineUsers", "RelativePath": "api/Presence/online", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.PresenceController", "Method": "UpdatePresence", "RelativePath": "api/Presence/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ChattrixBackend.Controllers.UpdatePresenceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.PresenceController", "Method": "GetUserPresence", "RelativePath": "api/Presence/status/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ChattrixBackend.Core.Entities.UserManagement.ResponseModel.Response", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.RolesController", "Method": "CreateRole", "RelativePath": "api/Roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Role", "Type": "ChattrixBackend.Core.Entities.UserManagement.Roles.Roles", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.RolesController", "Method": "GetRoles", "RelativePath": "api/Roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ChattrixBackend.Core.Entities.UserManagement.Roles.RoleResponse, ChattrixBackend.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ChattrixBackend.Controllers.RolesController", "Method": "DeleteRole", "RelativePath": "api/Roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ChattrixBackend.Controllers.RolesController", "Method": "AssignRole", "RelativePath": "api/Roles/assign", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleAssign", "Type": "ChattrixBackend.Core.Entities.UserManagement.Roles.RoleAssign", "IsRequired": true}], "ReturnTypes": []}]