/* User Details Container */
.user-details-container {
  padding: var(--spacing-lg);
  background: var(--bg-main-content); /* Light grayish-white background */
  min-height: 100%; /* Take full height of parent */
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative; /* Ensure proper positioning context */
}

/* Header Section */
.header-section {
  margin-bottom: var(--spacing-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.header-navigation {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.back-btn {
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);

  &:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
  }
}

.header-text {
  flex: 1;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.edit-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */

  mat-icon {
    margin-right: var(--spacing-xs);
    color: #ffffff !important;
  }

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    color: #ffffff !important;
    border-color: #ffffff !important;
  }
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-md);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Form Section */
.form-section {
  width: 100%;
  margin: 0 auto;
}

.form-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  width: 800px;
}

/* Integrated Header Section */
.integrated-header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md)
    var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
  margin-bottom: var(--spacing-lg);
  background: var(--bg-card); /* Match card background */
}

.header-content {
  width: 100%;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

/* Form Section Groups */
.form-section-group {
  margin-bottom: var(--spacing-xl);
  padding: 0 var(--spacing-lg);
}

.section-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Form Rows */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);

  &:last-child {
    margin-bottom: 0;
  }
}

/* Display Fields */
.display-field {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  transition: all 0.2s ease;

  &.full-width {
    grid-column: 1 / -1;
  }

  &:hover {
    border-color: var(--border-primary);
    background: var(--bg-hover);
  }
}

.field-icon {
  color: var(--text-secondary);
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.field-content {
  flex: 1;
  min-width: 0;
}

.field-label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-value {
  color: var(--text-primary);
  font-size: 1rem;
  line-height: 1.5;
  word-break: break-word;
}

/* Profile Picture Container */
.profile-picture-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md) 0;
}

/* Profile Section */
.profile-section {
  margin-bottom: var(--spacing-lg);
}

.profile-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
}

.profile-picture {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 3px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.placeholder-icon {
  font-size: 4rem;
  color: var(--text-muted);
  opacity: 0.3;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.initials {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 2;
  position: relative;
}

.basic-info {
  flex: 1;
}

.user-name {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-email {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0 0 var(--spacing-md) 0;
}

.status-badge {
  display: flex;
  align-items: center;
}

/* Status Chip Styling */
:host ::ng-deep .status-active {
  background: #e8f5e8;
  color: #2e7d32;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 40px;
  font-weight: 500;

  mat-icon {
    color: #2e7d32;
    font-size: 1.125rem;
    width: 1.125rem;
    height: 1.125rem;
    line-height: 1;
  }
}

:host ::ng-deep .status-inactive {
  background: #ffebee;
  color: #c62828;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 40px;
  font-weight: 500;

  mat-icon {
    color: #c62828;
    font-size: 1.125rem;
    width: 1.125rem;
    height: 1.125rem;
    line-height: 1;
  }
}

/* Details Grid */
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.info-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

:host ::ng-deep .info-card {
  .mat-mdc-card-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md)
      var(--spacing-lg);
  }

  .mat-mdc-card-content {
    padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
  }

  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;

    mat-icon {
      color: var(--text-secondary);
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-secondary);

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
}

.info-value {
  color: var(--text-primary);
  font-size: 0.875rem;
  text-align: right;
  word-break: break-word;
}

.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

/* General Chip Alignment Fix */
:host ::ng-deep .mat-mdc-chip {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: middle !important;

  mat-icon {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    vertical-align: middle !important;
  }

  .mdc-evolution-chip__text-label {
    display: inline-flex !important;
    align-items: center !important;
    vertical-align: middle !important;
  }
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-secondary);
  margin-top: var(--spacing-lg);
}

.back-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary);

  mat-icon {
    margin-right: var(--spacing-xs);
  }

  &:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
    border-color: var(--border-primary);
  }
}

.edit-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */

  mat-icon {
    margin-right: var(--spacing-xs);
    color: #ffffff !important;
  }

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    color: #ffffff !important;
    border-color: #ffffff !important;
  }
}

/* Role Chip Styling */
:host ::ng-deep .roles-container {
  .mat-mdc-chip {
    margin: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex !important;
    align-items: center !important;
    gap: var(--spacing-xs) !important;
    padding: var(--spacing-xs) var(--spacing-sm) !important;
    border-radius: 40px !important;
    min-height: 32px !important;
    line-height: 1 !important;

    mat-icon {
      font-size: 1rem !important;
      width: 1rem !important;
      height: 1rem !important;
      line-height: 1 !important;
      margin: 0 !important;
    }

    .mdc-evolution-chip__text-label {
      line-height: 1 !important;
      display: flex !important;
      align-items: center !important;
    }
  }

  .role-user {
    background: #e3f2fd;
    color: #1565c0;

    mat-icon {
      color: #1565c0 !important;
    }
  }

  .role-admin {
    background: #fff3e0;
    color: #ef6c00;

    mat-icon {
      color: #ef6c00 !important;
    }
  }

  .role-super-admin {
    background: #fce4ec;
    color: #ad1457;

    mat-icon {
      color: #ad1457 !important;
    }
  }
}

.description-text {
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
  padding: var(--spacing-sm) 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  gap: var(--spacing-md);
}

.error-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
}

.error-state h3 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.error-state p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details-container {
    padding: var(--spacing-md);
  }

  .form-section {
    max-width: 100%;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .display-field {
    padding: var(--spacing-sm);
  }

  .field-icon {
    font-size: 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }

  .profile-image,
  .profile-placeholder {
    width: 100px;
    height: 100px;
  }

  .placeholder-icon {
    font-size: 3rem;
  }

  .initials {
    font-size: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .back-btn,
  .edit-btn {
    width: 100%;
    justify-content: center;
  }

  .section-title {
    font-size: 1.125rem;
  }

  .field-label {
    font-size: 0.8rem;
  }

  .field-value {
    font-size: 0.9rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .user-details-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .profile-card,
  .info-card {
    background: #ffffff; /* White cards */
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .page-title {
    color: #333333;
  }

  .page-subtitle {
    color: #666666;
  }

  .back-btn {
    color: #666666;

    &:hover {
      color: #333333;
      background: #f9f9f9;
    }
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .profile-placeholder {
    background: #f8f9fa;
    border-color: #dee2e6;
  }

  .placeholder-icon {
    color: #6c757d;
  }

  .initials {
    color: #212529;
  }

  .profile-image {
    border-color: #dee2e6;
  }

  .info-label {
    color: #666666;
  }

  .info-value {
    color: #333333;
  }

  .description-text {
    color: #333333;
  }

  .loading-text {
    color: #666666;
  }

  .error-icon {
    color: #999999;
  }

  .error-state h3 {
    color: #333333;
  }

  .error-state p {
    color: #666666;
  }

  .info-row {
    border-bottom-color: #f0f0f0;
  }

  :host ::ng-deep .info-card {
    .mat-mdc-card-title {
      color: #333333;

      mat-icon {
        color: #666666;
      }
    }
  }

  :host ::ng-deep .status-active {
    background: #e8f5e8;
    color: #2e7d32;

    mat-icon {
      color: #2e7d32 !important;
    }
  }

  :host ::ng-deep .status-inactive {
    background: #ffebee;
    color: #c62828;

    mat-icon {
      color: #c62828 !important;
    }
  }
}

/* Dark Theme Overrides */
:host-context(.dark-theme) {
  .user-details-container {
    background: var(--bg-main-content); /* Keep main background consistent */
  }

  .profile-card,
  .info-card {
    background: #2a2a2a; /* Greyish background for dark mode */
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .page-title {
    color: #ffffff;
  }

  .page-subtitle {
    color: #b0b0b0;
  }

  .back-btn {
    color: #b0b0b0;

    &:hover {
      color: #ffffff;
      background: #353535;
    }
  }

  .user-name {
    color: #ffffff;
  }

  .user-email {
    color: #b0b0b0;
  }

  .profile-placeholder {
    background: #343a40;
    border-color: #495057;
  }

  .placeholder-icon {
    color: #6c757d;
  }

  .initials {
    color: #ffffff;
  }

  .profile-image {
    border-color: #495057;
  }

  .info-label {
    color: #b0b0b0;
  }

  .info-value {
    color: #ffffff;
  }

  .description-text {
    color: #ffffff;
  }

  .loading-text {
    color: #b0b0b0;
  }

  .error-icon {
    color: #666666;
  }

  .error-state h3 {
    color: #ffffff;
  }

  .error-state p {
    color: #b0b0b0;
  }

  .info-row {
    border-bottom-color: #404040;
  }

  :host ::ng-deep .info-card {
    .mat-mdc-card-title {
      color: #ffffff;

      mat-icon {
        color: #b0b0b0;
      }
    }
  }

  :host ::ng-deep .status-active {
    background: #1b4332;
    color: #4caf50;

    mat-icon {
      color: #4caf50 !important;
    }
  }

  :host ::ng-deep .status-inactive {
    background: #4a1e1e;
    color: #f44336;

    mat-icon {
      color: #f44336 !important;
    }
  }

  :host ::ng-deep .roles-container {
    .role-user {
      background: #1e3a8a;
      color: #60a5fa;

      mat-icon {
        color: #60a5fa !important;
      }
    }

    .role-admin {
      background: #92400e;
      color: #fbbf24;
    }

    .role-super-admin {
      background: #7c2d12;
      color: #f87171;
    }
  }
}
