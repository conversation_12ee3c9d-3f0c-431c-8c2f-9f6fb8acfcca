import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, map, distinctUntilChanged } from 'rxjs';
import { AuthState, UserInfo } from '../Models';

@Injectable({
  providedIn: 'root'
})
export class AuthStateService {
  
  // Private subjects for state management
  private readonly _isAuthenticated$ = new BehaviorSubject<boolean>(false);
  private readonly _isLoading$ = new BehaviorSubject<boolean>(false);
  private readonly _user$ = new BehaviorSubject<UserInfo | null>(null);
  private readonly _token$ = new BehaviorSubject<string | null>(null);
  private readonly _error$ = new BehaviorSubject<string | null>(null);

  // Public observables
  readonly isAuthenticated$ = this._isAuthenticated$.asObservable().pipe(distinctUntilChanged());
  readonly isLoading$ = this._isLoading$.asObservable().pipe(distinctUntilChanged());
  readonly user$ = this._user$.asObservable().pipe(distinctUntilChanged());
  readonly token$ = this._token$.asObservable().pipe(distinctUntilChanged());
  readonly error$ = this._error$.asObservable().pipe(distinctUntilChanged());

  // Combined state observable
  readonly authState$: Observable<AuthState> = combineLatest([
    this.isAuthenticated$,
    this.isLoading$,
    this.user$,
    this.token$,
    this.error$
  ]).pipe(
    map(([isAuthenticated, isLoading, user, token, error]) => ({
      isAuthenticated,
      isLoading,
      user,
      token,
      error
    })),
    distinctUntilChanged((prev, curr) => 
      prev.isAuthenticated === curr.isAuthenticated &&
      prev.isLoading === curr.isLoading &&
      prev.user === curr.user &&
      prev.token === curr.token &&
      prev.error === curr.error
    )
  );

  // Getters for current state values
  get isAuthenticated(): boolean {
    return this._isAuthenticated$.value;
  }

  get isLoading(): boolean {
    return this._isLoading$.value;
  }

  get user(): UserInfo | null {
    return this._user$.value;
  }

  get token(): string | null {
    return this._token$.value;
  }

  get error(): string | null {
    return this._error$.value;
  }

  get currentState(): AuthState {
    return {
      isAuthenticated: this.isAuthenticated,
      isLoading: this.isLoading,
      user: this.user,
      token: this.token,
      error: this.error
    };
  }

  /**
   * Sets the authentication status
   */
  setAuthenticated(isAuthenticated: boolean): void {
    this._isAuthenticated$.next(isAuthenticated);
  }

  /**
   * Sets the loading status
   */
  setLoading(isLoading: boolean): void {
    this._isLoading$.next(isLoading);
  }

  /**
   * Sets the current user information
   */
  setUser(user: UserInfo | null): void {
    this._user$.next(user);
  }

  /**
   * Sets the authentication token
   */
  setToken(token: string | null): void {
    this._token$.next(token);
  }

  /**
   * Sets the error message
   */
  setError(error: string | null): void {
    this._error$.next(error);
  }

  /**
   * Clears the error state
   */
  clearError(): void {
    this._error$.next(null);
  }

  /**
   * Sets the complete authentication state
   */
  setState(state: Partial<AuthState>): void {
    if (state.isAuthenticated !== undefined) {
      this.setAuthenticated(state.isAuthenticated);
    }
    if (state.isLoading !== undefined) {
      this.setLoading(state.isLoading);
    }
    if (state.user !== undefined) {
      this.setUser(state.user);
    }
    if (state.token !== undefined) {
      this.setToken(state.token);
    }
    if (state.error !== undefined) {
      this.setError(state.error);
    }
  }

  /**
   * Resets the authentication state to initial values
   */
  reset(): void {
    this._isAuthenticated$.next(false);
    this._isLoading$.next(false);
    this._user$.next(null);
    this._token$.next(null);
    this._error$.next(null);
  }

  /**
   * Sets the state for successful login
   */
  setLoginSuccess(user: UserInfo, token: string): void {
    this.setState({
      isAuthenticated: true,
      isLoading: false,
      user,
      token,
      error: null
    });
  }

  /**
   * Sets the state for login failure
   */
  setLoginFailure(error: string): void {
    this.setState({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      token: null,
      error
    });
  }

  /**
   * Sets the state for logout
   */
  setLogoutState(): void {
    this.reset();
  }

  /**
   * Observable that emits when user roles change
   */
  readonly userRoles$ = this.user$.pipe(
    map(user => user?.role || []),
    distinctUntilChanged()
  );

  /**
   * Observable that emits when user is admin
   */
  readonly isAdmin$ = this.userRoles$.pipe(
    map(roles => {
      if (Array.isArray(roles)) {
        return roles.includes('Admin') || roles.includes('admin');
      }
      return roles === 'Admin' || roles === 'admin';
    }),
    distinctUntilChanged()
  );

  /**
   * Observable that emits when authentication is required
   */
  readonly authRequired$ = this.isAuthenticated$.pipe(
    map(isAuth => !isAuth),
    distinctUntilChanged()
  );
}
