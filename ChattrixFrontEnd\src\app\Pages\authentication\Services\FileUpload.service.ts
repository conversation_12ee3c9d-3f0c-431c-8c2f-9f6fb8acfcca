import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, finalize } from 'rxjs';
import { environment } from '../../../../Environments/environment';
import { AuthErrorHandlerService } from './AuthErrorHandler.service';
import { AuthStateService } from './AuthState.service';
import { RegisterResponse } from '../Models';

@Injectable({
  providedIn: 'root',
})
export class FileUploadService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly errorHandler: AuthErrorHandlerService,
    private readonly authState: AuthStateService,
  ) {}

  /**
   * Register user with profile data including file upload
   */
  registerWithProfile(formData: FormData): Observable<RegisterResponse> {
    this.authState.setLoading(true);
    this.authState.clearError();

    // Don't set Content-Type header - let browser set it with boundary for multipart/form-data
    const headers = new HttpHeaders();

    return this.httpClient
      .post<RegisterResponse>(`${this.API_URL}/Register`, formData, { headers })
      .pipe(
        catchError((error) => {
          const authError = this.errorHandler.handleError(error);
          this.authState.setError(authError.message);
          return this.errorHandler.createErrorObservable(error);
        }),
        finalize(() => {
          this.authState.setLoading(false);
        }),
      );
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid image file (JPEG, JPG, or PNG).',
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size must be less than 5MB.',
      };
    }

    return { isValid: true };
  }

  /**
   * Create image preview from file
   */
  createImagePreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * Prepare FormData for registration with profile
   */
  prepareRegistrationFormData(
    signupData: any,
    description: string,
    profileImage?: File,
  ): FormData {
    const formData = new FormData();

    // Add basic signup data
    formData.append('fullName', signupData.fullName);
    formData.append('email', signupData.email);
    formData.append('password', signupData.password);

    if (signupData.phoneNumber) {
      formData.append('phoneNumber', signupData.phoneNumber);
    }

    if (description) {
      formData.append('description', description);
    }

    if (profileImage) {
      formData.append('profileImage', profileImage);
    }

    return formData;
  }
}
